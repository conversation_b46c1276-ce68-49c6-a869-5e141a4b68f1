# PowerShell script for Docker production environment management
# Version: 2.0 - Optimized and Enhanced

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true, Position=0)]
    [ValidateSet("up", "down", "build", "logs", "restart", "clean", "deploy", "status", "health")]
    [string]$Action,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("docker-compose.prod.yml", "docker-compose.staging.yml", "docker-compose.yml")]
    [string]$ComposeFile = "docker-compose.prod.yml",
    
    [Parameter(Mandatory=$false)]
    [switch]$Follow,
    
    [Parameter(Mandatory=$false)]
    [int]$Timeout = 60
)

# Set error handling
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Constants
$FRONTEND_PORT = 3001
$BACKEND_PORT = 5001
$MONGODB_PORT = 27017
$ENV_FILE_PATH = "ai-blog-platform-backend\.env"

# Logging functions with timestamps
function Write-Info {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] 🐳 $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow
}

# Validation functions
function Test-DockerRunning {
    try {
        $dockerInfo = docker version --format "{{.Server.Version}}" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Info "Docker is running (Version: $dockerInfo)"
            return $true
        }
    }
    catch {
        # Fallback check
    }
    
    Write-Error "Docker is not running or not accessible. Please start Docker Desktop and ensure it's properly configured."
    return $false
}

function Test-ComposeFile {
    if (-not (Test-Path $ComposeFile)) {
        Write-Error "Docker Compose file '$ComposeFile' not found in current directory."
        Write-Info "Available compose files:"
        Get-ChildItem -Name "docker-compose*.yml" | ForEach-Object { Write-Host "  - $_" }
        return $false
    }
    Write-Info "Using compose file: $ComposeFile"
    return $true
}

function Test-EnvironmentFiles {
    $missingFiles = @()
    
    if (-not (Test-Path $ENV_FILE_PATH)) {
        $missingFiles += $ENV_FILE_PATH
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-Error "Missing environment files:"
        $missingFiles | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
        Write-Info "Please create the required .env files before proceeding."
        return $false
    }
    
    Write-Info "Environment files validated successfully."
    return $true
}

function Wait-ForHealthyServices {
    param([int]$TimeoutSeconds = $Timeout)
    
    Write-Info "Waiting for services to be healthy (timeout: ${TimeoutSeconds}s)..."
    $startTime = Get-Date
    $healthyServices = @()
    
    do {
        try {
            $services = docker-compose -f $ComposeFile ps --format "table {{.Name}}\t{{.Status}}" | Select-Object -Skip 1
            $healthyServices = $services | Where-Object { $_ -match "Up|healthy" }
            
            if ($services.Count -eq $healthyServices.Count -and $services.Count -gt 0) {
                Write-Success "All services are healthy!"
                return $true
            }
            
            Start-Sleep 5
            $elapsed = (Get-Date) - $startTime
        }
        catch {
            Write-Warning "Error checking service status: $_"
            Start-Sleep 5
            $elapsed = (Get-Date) - $startTime
        }
    } while ($elapsed.TotalSeconds -lt $TimeoutSeconds)
    
    Write-Warning "Timeout reached. Some services may not be fully healthy yet."
    return $false
}

function Show-ServiceUrls {
    Write-Success "Services are accessible at:"
    Write-Host "  Frontend:  http://localhost:$FRONTEND_PORT" -ForegroundColor White
    Write-Host "  Backend:   http://localhost:$BACKEND_PORT" -ForegroundColor White
    Write-Host "  MongoDB:   mongodb://localhost:$MONGODB_PORT" -ForegroundColor White
}

function Get-ServiceStatus {
    Write-Info "Current service status:"
    try {
        docker-compose -f $ComposeFile ps
        
        Write-Info "Resource usage:"
        docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
    }
    catch {
        Write-Error "Failed to get service status: $_"
    }
}

# Pre-flight checks
function Invoke-PreFlightChecks {
    Write-Info "Running pre-flight checks..."
    
    if (-not (Test-DockerRunning)) { exit 1 }
    if (-not (Test-ComposeFile)) { exit 1 }
    if (-not (Test-EnvironmentFiles)) { exit 1 }
    
    Write-Success "Pre-flight checks passed!"
}

# Main execution logic
try {
    Write-Info "Starting Docker management script - Action: $Action"
    
    # Run checks for most actions (skip for logs and status)
    if ($Action -notin @("logs", "status")) {
        Invoke-PreFlightChecks
    }

    switch ($Action) {
        "up" {
            Write-Info "Starting production environment..."
            docker-compose -f $ComposeFile up -d
            if ($LASTEXITCODE -eq 0) {
                Wait-ForHealthyServices
                Write-Success "Production environment started successfully!"
                Show-ServiceUrls
            } else {
                Write-Error "Failed to start production environment."
                exit 1
            }
        }
        
        "down" {
            Write-Info "Stopping production environment..."
            docker-compose -f $ComposeFile down
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Production environment stopped successfully!"
            } else {
                Write-Error "Failed to stop production environment."
                exit 1
            }
        }
        
        "build" {
            Write-Info "Building production images..."
            docker-compose -f $ComposeFile build --no-cache --parallel
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Production images built successfully!"
            } else {
                Write-Error "Failed to build production images."
                exit 1
            }
        }
        
        "logs" {
            Write-Info "Showing logs$(if ($Follow) { ' (following)' })..."
            if ($Follow) {
                docker-compose -f $ComposeFile logs -f
            } else {
                docker-compose -f $ComposeFile logs --tail=100
            }
        }
        
        "restart" {
            Write-Info "Restarting production environment..."
            docker-compose -f $ComposeFile restart
            if ($LASTEXITCODE -eq 0) {
                Wait-ForHealthyServices
                Write-Success "Production environment restarted successfully!"
                Show-ServiceUrls
            } else {
                Write-Error "Failed to restart production environment."
                exit 1
            }
        }
        
        "clean" {
            Write-Info "Cleaning up production environment..."
            Write-Warning "This will remove all containers, volumes, and unused images!"
            
            $confirm = Read-Host "Are you sure? (y/N)"
            if ($confirm -eq 'y' -or $confirm -eq 'Y') {
                docker-compose -f $ComposeFile down -v --remove-orphans
                docker system prune -f --volumes
                docker image prune -a -f
                Write-Success "Production environment cleaned successfully!"
            } else {
                Write-Info "Clean operation cancelled."
            }
        }
        
        "deploy" {
            Write-Info "Deploying production environment..."
            
            Write-Info "Building images..."
            docker-compose -f $ComposeFile build --parallel
            
            if ($LASTEXITCODE -eq 0) {
                Write-Info "Starting services..."
                docker-compose -f $ComposeFile up -d
                
                if ($LASTEXITCODE -eq 0) {
                    Wait-ForHealthyServices
                    Write-Success "Production deployment completed successfully!"
                    Show-ServiceUrls
                } else {
                    Write-Error "Failed to start services during deployment."
                    exit 1
                }
            } else {
                Write-Error "Failed to build images during deployment."
                exit 1
            }
        }
        
        "status" {
            Get-ServiceStatus
        }
        
        "health" {
            Write-Info "Checking service health..."
            try {
                $services = docker-compose -f $ComposeFile ps --services
                foreach ($service in $services) {
                    $health = docker-compose -f $ComposeFile exec -T $service echo "OK" 2>$null
                    if ($LASTEXITCODE -eq 0) {
                        Write-Success "$service is healthy"
                    } else {
                        Write-Error "$service is not responding"
                    }
                }
            }
            catch {
                Write-Error "Failed to check service health: $_"
            }
        }
    }
    
    Write-Success "Docker management script completed successfully!"
    
} catch {
    Write-Error "An unexpected error occurred: $_"
    Write-Error $_.ScriptStackTrace
    exit 1
}