# syntax=docker/dockerfile:1
FROM node:18-alpine

# Install minimal runtime dependencies
RUN apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeuser -u 1001

WORKDIR /app

# Copy package files first for better caching
COPY --chown=nodeuser:nodejs package*.json ./

# Install ONLY production dependencies
RUN npm ci --only=production --omit=dev && \
    npm cache clean --force && \
    rm -rf ~/.npm /tmp/*

# Copy application code
COPY --chown=nodeuser:nodejs server.js check-env.js ./
COPY --chown=nodeuser:nodejs controllers/ ./controllers/
COPY --chown=nodeuser:nodejs middleware/ ./middleware/
COPY --chown=nodeuser:nodejs models/ ./models/
COPY --chown=nodeuser:nodejs routes/ ./routes/
COPY --chown=nodeuser:nodejs services/ ./services/
COPY --chown=nodeuser:nodejs scripts/ ./scripts/
COPY --chown=nodeuser:nodejs service_account_key.json* ./

# Create required directories
RUN mkdir -p uploads logs && \
    chown nodeuser:nodejs uploads logs && \
    if [ -f service_account_key.json ]; then chmod 600 service_account_key.json; fi

EXPOSE 5001
USER nodeuser

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:5001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]

