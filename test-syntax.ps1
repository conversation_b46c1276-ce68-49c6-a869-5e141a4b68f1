# Test PowerShell syntax
Write-Host "Testing PowerShell syntax..." -ForegroundColor Green

# Test the functions from deploy-production.ps1
function Write-Step {
    param([string]$Message)
    Write-Host "🚀 STEP: $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Yellow
}

# Test the problematic lines
Write-Info "Use docker images to verify optimized sizes"
Write-Info "Target: Backend ~60MB, Frontend ~80MB"

Write-Host "✅ Syntax test passed!" -ForegroundColor Green
