# PowerShell script for Docker production environment management

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("up", "down", "build", "logs", "restart", "clean", "deploy")]
    [string]$Action
)

$ErrorActionPreference = "Stop"

function Write-Info {
    param([string]$Message)
    Write-Host "🐳 $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

# Check if Docker is running
try {
    docker info | Out-Null
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop."
    exit 1
}

# Check if .env file exists
if (-not (Test-Path "ai-blog-platform-backend\.env")) {
    Write-Error "Backend .env file not found. Please create one in ai-blog-platform-backend directory."
    exit 1
}

switch ($Action) {
    "up" {
        Write-Info "Starting production environment..."
        docker-compose -f docker-compose.prod.yml up -d
        Write-Success "Production environment started!"
        Write-Info "Frontend: http://localhost:3001"
        Write-Info "Backend: http://localhost:5001"
        Write-Info "MongoDB: mongodb://localhost:27017"
    }
    
    "down" {
        Write-Info "Stopping production environment..."
        docker-compose -f docker-compose.prod.yml down
        Write-Success "Production environment stopped!"
    }
    
    "build" {
        Write-Info "Building production images..."
        docker-compose -f docker-compose.prod.yml build --no-cache
        Write-Success "Production images built!"
    }
    
    "logs" {
        Write-Info "Showing logs..."
        docker-compose -f docker-compose.prod.yml logs -f
    }
    
    "restart" {
        Write-Info "Restarting production environment..."
        docker-compose -f docker-compose.prod.yml restart
        Write-Success "Production environment restarted!"
    }
    
    "clean" {
        Write-Info "Cleaning up production environment..."
        docker-compose -f docker-compose.prod.yml down -v
        docker system prune -f
        Write-Success "Production environment cleaned!"
    }
    
    "deploy" {
        Write-Info "Deploying production environment..."
        Write-Info "Building images..."
        docker-compose -f docker-compose.prod.yml build
        Write-Info "Starting services..."
        docker-compose -f docker-compose.prod.yml up -d
        Write-Info "Waiting for services to be healthy..."
        Start-Sleep 30
        Write-Success "Production deployment complete!"
        Write-Info "Frontend: http://localhost:3001"
        Write-Info "Backend: http://localhost:5001"
    }
}
