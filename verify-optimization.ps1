# Script to verify Docker optimization results

Write-Host "🔍 Docker Image Size Verification" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Get current image sizes
$images = docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | Select-String "blog-platform|mongo"

if (-not $images) {
    Write-Host "❌ No blog-platform images found. Run deployment first:" -ForegroundColor Red
    Write-Host "   .\deploy-production.ps1 build" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n📊 Current Image Sizes:" -ForegroundColor Green
$images | ForEach-Object { Write-Host $_ -ForegroundColor White }

# Parse sizes and check optimization targets
$backendSize = $null
$frontendSize = $null

foreach ($line in $images) {
    if ($line -match "blog-platform-backend.*?(\d+(?:\.\d+)?)(MB|GB)") {
        $size = [float]$matches[1]
        $unit = $matches[2]
        if ($unit -eq "GB") { $size *= 1024 }
        $backendSize = $size
    }
    if ($line -match "blog-platform-frontend.*?(\d+(?:\.\d+)?)(MB|GB)") {
        $size = [float]$matches[1]
        $unit = $matches[2]
        if ($unit -eq "GB") { $size *= 1024 }
        $frontendSize = $size
    }
}

Write-Host "`n🎯 Optimization Targets vs Actual:" -ForegroundColor Cyan

# Backend verification
if ($backendSize) {
    $backendStatus = if ($backendSize -le 80) { "✅ EXCELLENT" } 
                    elseif ($backendSize -le 120) { "✅ GOOD" }
                    elseif ($backendSize -le 200) { "⚠️ ACCEPTABLE" }
                    else { "❌ NEEDS OPTIMIZATION" }
    
    Write-Host "Backend:  Target ≤80MB  | Actual: ${backendSize}MB | $backendStatus" -ForegroundColor $(if ($backendSize -le 120) { "Green" } elseif ($backendSize -le 200) { "Yellow" } else { "Red" })
} else {
    Write-Host "Backend:  Image not found" -ForegroundColor Red
}

# Frontend verification
if ($frontendSize) {
    $frontendStatus = if ($frontendSize -le 100) { "✅ EXCELLENT" }
                     elseif ($frontendSize -le 150) { "✅ GOOD" }
                     elseif ($frontendSize -le 250) { "⚠️ ACCEPTABLE" }
                     else { "❌ NEEDS OPTIMIZATION" }
    
    Write-Host "Frontend: Target ≤100MB | Actual: ${frontendSize}MB | $frontendStatus" -ForegroundColor $(if ($frontendSize -le 150) { "Green" } elseif ($frontendSize -le 250) { "Yellow" } else { "Red" })
} else {
    Write-Host "Frontend: Image not found" -ForegroundColor Red
}

# Total size calculation
if ($backendSize -and $frontendSize) {
    $totalSize = $backendSize + $frontendSize
    $totalStatus = if ($totalSize -le 200) { "✅ EXCELLENT" }
                  elseif ($totalSize -le 300) { "✅ GOOD" }
                  elseif ($totalSize -le 500) { "⚠️ ACCEPTABLE" }
                  else { "❌ NEEDS OPTIMIZATION" }
    
    Write-Host "Total:    Target ≤200MB | Actual: ${totalSize}MB | $totalStatus" -ForegroundColor $(if ($totalSize -le 300) { "Green" } elseif ($totalSize -le 500) { "Yellow" } else { "Red" })
    
    # Calculate improvement from original sizes
    $originalTotal = 1392 # 413MB + 979MB from your screenshot
    $improvement = [math]::Round((($originalTotal - $totalSize) / $originalTotal) * 100, 1)
    Write-Host "`n📈 Size Reduction: ${improvement}% improvement from original 1.4GB" -ForegroundColor Green
}

Write-Host "`n💡 Optimization Tips:" -ForegroundColor Cyan
if ($backendSize -gt 120) {
    Write-Host "   • Backend: Check for dev dependencies in production build" -ForegroundColor Yellow
}
if ($frontendSize -gt 150) {
    Write-Host "   • Frontend: Verify Next.js standalone build is working" -ForegroundColor Yellow
}

Write-Host "`n🚀 To rebuild if needed:" -ForegroundColor Cyan
Write-Host "   .\deploy-production.ps1 build" -ForegroundColor White
