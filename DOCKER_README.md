# Docker Setup for AI Blog Platform

This document provides comprehensive instructions for running the AI Blog Platform using Docker containers.

## 🏗️ Architecture

The application consists of three main services:
- **Frontend**: Next.js application (Port 3001)
- **Backend**: Node.js/Express API (Port 5001)
- **Database**: MongoDB (Port 27017)

## 📋 Prerequisites

1. **Docker Desktop** installed and running
2. **Git** for cloning the repository
3. **PowerShell** (Windows) for running management scripts

## 🚀 Quick Start

### 1. Environment Setup

Copy the example environment file and configure it:
```powershell
Copy-Item .env.example .env
```

Edit `.env` file with your actual configuration values:
- API keys (Gemini, Perplexity, SERP, etc.)
- Google Cloud credentials
- AWS credentials
- WordPress credentials

### 2. Development Environment

Start the development environment with hot reloading:
```powershell
.\scripts\docker-dev.ps1 up
```

This will:
- Start MongoDB with development database
- Start backend with nodemon for auto-restart
- Start frontend with Next.js dev server
- Mount source code for live editing

Access the application:
- Frontend: http://localhost:3001
- Backend API: http://localhost:5001
- MongoDB: mongodb://localhost:27017

### 3. Production Environment

Deploy the production environment:
```powershell
.\scripts\docker-prod.ps1 deploy
```

This will:
- Build optimized production images
- Start all services with production configuration
- Set up proper health checks and restart policies

## 📜 Available Scripts

### Development Scripts

```powershell
# Start development environment
.\scripts\docker-dev.ps1 up

# Stop development environment
.\scripts\docker-dev.ps1 down

# Rebuild development images
.\scripts\docker-dev.ps1 build

# View logs
.\scripts\docker-dev.ps1 logs

# Restart services
.\scripts\docker-dev.ps1 restart

# Clean up (removes volumes)
.\scripts\docker-dev.ps1 clean
```

### Production Scripts

```powershell
# Deploy production environment
.\scripts\docker-prod.ps1 deploy

# Start production environment
.\scripts\docker-prod.ps1 up

# Stop production environment
.\scripts\docker-prod.ps1 down

# Rebuild production images
.\scripts\docker-prod.ps1 build

# View logs
.\scripts\docker-prod.ps1 logs

# Restart services
.\scripts\docker-prod.ps1 restart

# Clean up (removes volumes)
.\scripts\docker-prod.ps1 clean
```

### Health Check

```powershell
# Check development environment health
.\scripts\docker-health-check.ps1 dev

# Check production environment health
.\scripts\docker-health-check.ps1 prod
```

## 🔧 Configuration

### Environment Variables

Key environment variables to configure:

#### MongoDB
- `MONGO_ROOT_USERNAME`: MongoDB admin username
- `MONGO_ROOT_PASSWORD`: MongoDB admin password
- `MONGO_DB_NAME`: Database name

#### API Keys
- `GEMINI_API_KEY`: Google Gemini API key
- `PERPLEXITY_API_KEY`: Perplexity API key
- `SERP_API_KEY`: SERP API key
- `SERPER_API_KEY`: SERPER API key

#### Google Cloud
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to service account key
- `GOOGLE_PROJECT_ID`: Google Cloud project ID

#### AWS
- `AWS_ACCESS_KEY_ID`: AWS access key
- `AWS_SECRET_ACCESS_KEY`: AWS secret key
- `AWS_REGION`: AWS region
- `AWS_S3_BUCKET`: S3 bucket name

### Service Account Key

Place your Google Cloud service account key file as:
```
ai-blog-platform-backend/service_account_key.json
```

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3001, 5001, and 27017 are available
2. **Environment variables**: Check that all required variables are set in `.env`
3. **Service account key**: Ensure the file exists and has correct permissions
4. **Docker resources**: Ensure Docker has sufficient memory allocated

### Debugging

View container logs:
```powershell
docker-compose -f docker-compose.dev.yml logs [service-name]
```

Access container shell:
```powershell
docker exec -it [container-name] sh
```

Check container health:
```powershell
docker inspect [container-name] --format='{{.State.Health.Status}}'
```

## 📁 File Structure

```
├── ai-blog-platform-backend/
│   ├── Dockerfile              # Production backend image
│   ├── Dockerfile.dev          # Development backend image
│   └── .dockerignore          # Backend Docker ignore
├── ai-blog-platform-frontend/
│   ├── Dockerfile              # Production frontend image
│   ├── Dockerfile.dev          # Development frontend image
│   └── .dockerignore          # Frontend Docker ignore
├── scripts/
│   ├── docker-dev.ps1         # Development management
│   ├── docker-prod.ps1        # Production management
│   └── docker-health-check.ps1 # Health checking
├── docker-compose.dev.yml     # Development configuration
├── docker-compose.prod.yml    # Production configuration
├── .env.example               # Environment template
└── DOCKER_README.md           # This file
```

## 🔒 Security Notes

- Never commit `.env` files to version control
- Use strong passwords for MongoDB
- Rotate API keys regularly
- Keep Docker images updated
- Use non-root users in containers (already configured)

## 📊 Monitoring

The setup includes health checks for all services:
- HTTP health endpoints for frontend/backend
- MongoDB ping for database
- Automatic restart on failure
- Startup dependency management

Use the health check script to monitor service status:
```powershell
.\scripts\docker-health-check.ps1 prod
```
