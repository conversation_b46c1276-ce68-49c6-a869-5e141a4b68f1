# Production Deployment Script - Optimized for Size & Performance

param(
    [ValidateSet("build", "deploy", "status", "logs", "clean")]
    [string]$Action = "deploy"
)

$ErrorActionPreference = "Stop"

function Write-Step {
    param([string]$Message)
    Write-Host "🚀 STEP: $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Yellow
}

# Check prerequisites
Write-Step "Checking Prerequisites"
try {
    docker info | Out-Null
    Write-Success "Docker is running"
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop."
    exit 1
}

if (-not (Test-Path "ai-blog-platform-backend\.env")) {
    Write-Error "Backend .env file not found. Please create one."
    exit 1
}

switch ($Action) {
    "build" {
        Write-Step "Building Optimized Production Images"
        
        # Clean old images first
        Write-Info "Removing old images to force fresh build..."
        docker rmi blog-platform-backend:latest blog-platform-frontend:latest 2>$null
        
        # Build with no cache for optimization
        Write-Info "Building backend (target: ~60MB)..."
        docker build -t blog-platform-backend:latest ./ai-blog-platform-backend --no-cache
        
        Write-Info "Building frontend (target: ~80MB)..."
        docker build -t blog-platform-frontend:latest ./ai-blog-platform-frontend --no-cache
        
        # Show sizes
        Write-Step "Image Sizes After Optimization"
        docker images | Select-String "blog-platform|mongo" | ForEach-Object {
            Write-Host $_ -ForegroundColor White
        }
        
        Write-Success "Build complete!"
    }
    
    "deploy" {
        Write-Step "Deploying Production Environment"
        
        # Stop existing containers
        Write-Info "Stopping existing containers..."
        docker-compose -f docker-compose.prod.yml down 2>$null
        
        # Build optimized images
        Write-Info "Building optimized images..."
        docker-compose -f docker-compose.prod.yml build --no-cache
        
        # Start services
        Write-Info "Starting production services..."
        docker-compose -f docker-compose.prod.yml up -d
        
        # Wait for health checks
        Write-Info "Waiting for services to be healthy..."
        $timeout = 120
        $elapsed = 0
        
        do {
            Start-Sleep 5
            $elapsed += 5
            $healthy = docker-compose -f docker-compose.prod.yml ps --format json | ConvertFrom-Json | Where-Object { $_.Health -eq "healthy" }
            $total = docker-compose -f docker-compose.prod.yml ps --format json | ConvertFrom-Json
            
            Write-Info "Health check: $($healthy.Count)/$($total.Count) services healthy"
            
            if ($healthy.Count -eq $total.Count) {
                break
            }
            
            if ($elapsed -ge $timeout) {
                Write-Error "Timeout waiting for services to be healthy"
                exit 1
            }
        } while ($true)
        
        Write-Success "Production deployment complete!"
        Write-Info "Frontend: http://localhost:3001"
        Write-Info "Backend: http://localhost:5001"
        Write-Info "MongoDB: mongodb://localhost:27017"
        
        # Show final image sizes
        Write-Step "Final Image Sizes"
        docker images | Select-String "blog-platform" | ForEach-Object {
            Write-Host $_ -ForegroundColor Green
        }
    }
    
    "status" {
        Write-Step "Production Environment Status"
        docker-compose -f docker-compose.prod.yml ps
        
        Write-Step "Image Sizes"
        docker images | Select-String "blog-platform|mongo"
        
        Write-Step "Resource Usage"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
    }
    
    "logs" {
        Write-Step "Production Logs"
        docker-compose -f docker-compose.prod.yml logs -f --tail=50
    }
    
    "clean" {
        Write-Step "Cleaning Production Environment"
        docker-compose -f docker-compose.prod.yml down -v
        docker system prune -f
        docker volume prune -f
        Write-Success "Environment cleaned!"
    }
}

Write-Info "Use docker images to verify optimized sizes"
Write-Info "Target: Backend ~60MB, Frontend ~80MB"
