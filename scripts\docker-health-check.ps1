# PowerShell script for checking Docker container health

param(
    [ValidateSet("dev", "prod")]
    [string]$Environment = "dev"
)

$ErrorActionPreference = "Stop"

function Write-Info {
    param([string]$Message)
    Write-Host "🔍 $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

$ComposeFile = if ($Environment -eq "dev") { "docker-compose.dev.yml" } else { "docker-compose.prod.yml" }

Write-Info "Checking health of $Environment environment..."

# Check if containers are running
$Containers = docker-compose -f $ComposeFile ps --format json | ConvertFrom-Json

if (-not $Containers) {
    Write-Error "No containers found. Is the environment running?"
    exit 1
}

foreach ($Container in $Containers) {
    $Name = $Container.Name
    $State = $Container.State
    $Status = $Container.Status
    
    Write-Info "Container: $Name"
    
    if ($State -eq "running") {
        Write-Success "  State: $State"
        
        # Check health status
        $HealthStatus = docker inspect $Name --format='{{.State.Health.Status}}' 2>$null
        if ($HealthStatus) {
            if ($HealthStatus -eq "healthy") {
                Write-Success "  Health: $HealthStatus"
            } else {
                Write-Warning "  Health: $HealthStatus"
            }
        } else {
            Write-Info "  Health: No health check configured"
        }
        
        # Check if ports are accessible
        if ($Name -like "*frontend*") {
            try {
                $Response = Invoke-WebRequest -Uri "http://localhost:3001" -TimeoutSec 5 -UseBasicParsing
                Write-Success "  Frontend accessible on port 3001"
            } catch {
                Write-Error "  Frontend not accessible on port 3001"
            }
        }
        
        if ($Name -like "*backend*") {
            try {
                $Response = Invoke-WebRequest -Uri "http://localhost:5001/health" -TimeoutSec 5 -UseBasicParsing
                Write-Success "  Backend accessible on port 5001"
            } catch {
                Write-Error "  Backend not accessible on port 5001"
            }
        }
        
        if ($Name -like "*mongodb*") {
            try {
                $MongoTest = docker exec $Name mongosh --eval "db.adminCommand('ping')" --quiet
                if ($LASTEXITCODE -eq 0) {
                    Write-Success "  MongoDB accessible on port 27017"
                } else {
                    Write-Error "  MongoDB not responding"
                }
            } catch {
                Write-Error "  MongoDB health check failed"
            }
        }
    } else {
        Write-Error "  State: $State"
        Write-Error "  Status: $Status"
    }
    
    Write-Host ""
}

Write-Info "Health check complete!"
