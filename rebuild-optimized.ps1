# Script to rebuild optimized Docker images

Write-Host "🔧 Rebuilding optimized Docker images..." -ForegroundColor Cyan

# Stop existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.prod.yml down 2>$null

# Remove old images to force rebuild
Write-Host "Removing old images..." -ForegroundColor Yellow
docker rmi blog_gen_backend blog_gen_frontend 2>$null

# Build new optimized images
Write-Host "Building optimized images..." -ForegroundColor Green
docker-compose -f docker-compose.prod.yml build --no-cache

# Show new image sizes
Write-Host "`n📊 New image sizes:" -ForegroundColor Cyan
docker images | Select-String "blog_gen|mongo" | ForEach-Object {
    Write-Host $_ -ForegroundColor White
}

# Calculate total size
$images = docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | Select-String "blog_gen"
Write-Host "`n✅ Optimization complete!" -ForegroundColor Green
Write-Host "Images should now be much smaller (target: <100MB each)" -ForegroundColor Green
