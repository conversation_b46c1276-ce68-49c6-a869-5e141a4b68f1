# Quick script to check Docker data storage

Write-Host "🔍 Checking Docker volumes..." -ForegroundColor Cyan

# List all volumes
Write-Host "`n📁 Available volumes:" -ForegroundColor Yellow
docker volume ls

# Check MongoDB data
Write-Host "`n🗄️ MongoDB data volume details:" -ForegroundColor Yellow
docker volume inspect blog_gen_mongodb_data 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "MongoDB volume not found - containers not started yet" -ForegroundColor Red
}

# Check backend uploads
Write-Host "`n📤 Backend uploads volume details:" -ForegroundColor Yellow
docker volume inspect blog_gen_backend_uploads 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "Backend uploads volume not found - containers not started yet" -ForegroundColor Red
}

# If containers are running, show data inside
Write-Host "`n🔍 Checking if containers are running..." -ForegroundColor Cyan
$containers = docker ps --format "table {{.Names}}\t{{.Status}}"
Write-Host $containers

# Check MongoDB data if container is running
$mongoContainer = docker ps --filter "name=mongodb" --format "{{.Names}}" | Select-Object -First 1
if ($mongoContainer) {
    Write-Host "`n📊 MongoDB databases:" -ForegroundColor Green
    docker exec $mongoContainer mongosh --eval "show dbs" --quiet
    
    Write-Host "`n📋 Collections in blog_platform:" -ForegroundColor Green
    docker exec $mongoContainer mongosh blog_platform --eval "show collections" --quiet 2>$null
}

# Check backend uploads if container is running
$backendContainer = docker ps --filter "name=backend" --format "{{.Names}}" | Select-Object -First 1
if ($backendContainer) {
    Write-Host "`n📁 Backend uploads directory:" -ForegroundColor Green
    docker exec $backendContainer ls -la /app/uploads 2>$null
}

Write-Host "`n✅ Data check complete!" -ForegroundColor Green
